using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Drawing;
using System.IO;

namespace Watcher
{
    /// <summary>
    /// 创建导出模板的辅助类
    /// </summary>
    public static class CreateTemplateHelper
    {
        /// <summary>
        /// 创建默认的导出模板文件
        /// </summary>
        /// <param name="templatePath">模板文件保存路径</param>
        public static void CreateDefaultTemplate(string templatePath)
        {
            try
            {
                // 确保目录存在
                var directory = Path.GetDirectoryName(templatePath);
                if (!string.IsNullOrEmpty(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 设置EPPlus许可证
                ExcelPackage.License.SetNonCommercialPersonal("<frey>");

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("收费记录模板");

                // 设置页面设置
                worksheet.PrinterSettings.PaperSize = ePaperSize.A4;
                worksheet.PrinterSettings.Orientation = eOrientation.Portrait;
                worksheet.PrinterSettings.FitToPage = true;
                worksheet.PrinterSettings.FitToWidth = 1;
                worksheet.PrinterSettings.FitToHeight = 0;

                // 设置页边距
                worksheet.PrinterSettings.LeftMargin = 0.5;
                worksheet.PrinterSettings.RightMargin = 0.5;
                worksheet.PrinterSettings.TopMargin = 0.75;
                worksheet.PrinterSettings.BottomMargin = 0.75;

                // 第一行：标题
                worksheet.Cells[1, 1].Value = "收费记录表";
                worksheet.Cells[1, 1, 1, 16].Merge = true;
                worksheet.Cells[1, 1].Style.Font.Size = 18;
                worksheet.Cells[1, 1].Style.Font.Bold = true;
                worksheet.Cells[1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[1, 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                worksheet.Row(1).Height = 30;

                // 第二行：表头
                // 设置4组表头，每组包含：序号、时间、金额、空白
                string[] headers = { "序号", "时间", "金额", "" };
                for (int group = 0; group < 4; group++)
                {
                    int baseCol = group * 4 + 1;
                    for (int i = 0; i < headers.Length; i++)
                    {
                        var cell = worksheet.Cells[2, baseCol + i];
                        cell.Value = headers[i];
                        cell.Style.Font.Bold = true;
                        cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        cell.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                        cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        cell.Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
                        cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    }
                }

                // 设置列宽
                for (int group = 0; group < 4; group++)
                {
                    int baseCol = group * 4 + 1;
                    worksheet.Column(baseCol).Width = 6;      // 序号列
                    worksheet.Column(baseCol + 1).Width = 10; // 时间列
                    worksheet.Column(baseCol + 2).Width = 8;  // 金额列
                    worksheet.Column(baseCol + 3).Width = 2;  // 空白列
                }

                // 设置数据行的格式（第3行到第52行，共50行数据）
                for (int row = 3; row <= 52; row++)
                {
                    worksheet.Row(row).Height = 18;
                    
                    for (int group = 0; group < 4; group++)
                    {
                        int baseCol = group * 4 + 1;
                        
                        // 序号列
                        var seqCell = worksheet.Cells[row, baseCol];
                        seqCell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        seqCell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                        
                        // 时间列
                        var timeCell = worksheet.Cells[row, baseCol + 1];
                        timeCell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        timeCell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                        
                        // 金额列
                        var amountCell = worksheet.Cells[row, baseCol + 2];
                        amountCell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        amountCell.Style.Numberformat.Format = "0.00";
                        amountCell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    }
                }

                // 设置打印时重复行（前两行）
                worksheet.PrinterSettings.RepeatRows = new ExcelAddress("1:2");

                // 保存文件
                var fileInfo = new FileInfo(templatePath);
                package.SaveAs(fileInfo);

                System.Diagnostics.Debug.WriteLine($"模板文件已创建: {templatePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建模板文件失败: {ex.Message}");
                throw;
            }
        }
    }
}
