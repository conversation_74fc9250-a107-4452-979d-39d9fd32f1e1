using System;
using System.Text.Json.Serialization;
using System.Collections.Generic;
using System.IO;

namespace Watcher.Models
{
    /// <summary>
    /// 应用程序配置类
    /// </summary>
    public class AppConfig
    {
        /// <summary>
        /// 上次打开的文件夹路径
        /// </summary>
        [JsonPropertyName("last_opened_folder")]
        public string LastOpenedFolder { get; set; } = string.Empty;

        /// <summary>
        /// 上次导出的文件夹路径
        /// </summary>
        [JsonPropertyName("last_export_folder")]
        public string LastExportFolder { get; set; } = string.Empty;

        /// <summary>
        /// FFmpeg路径
        /// </summary>
        [JsonPropertyName("ffmpeg_path")]
        public string FFmpegPath { get; set; } = string.Empty;

        /// <summary>
        /// 导出模板文件路径
        /// </summary>
        [JsonPropertyName("export_template_path")]
        public string ExportTemplatePath { get; set; } = string.Empty;

        /// <summary>
        /// 配置版本，用于未来兼容性
        /// </summary>
        [JsonPropertyName("version")]
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// 其他配置项扩展字典
        /// </summary>
        [JsonPropertyName("additional_settings")]
        public Dictionary<string, object> AdditionalSettings { get; set; } = new();

        public AppConfig()
        {
            // 设置默认值
            LastOpenedFolder = Environment.GetFolderPath(Environment.SpecialFolder.MyVideos);
            LastExportFolder = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);

            // 设置默认FFmpeg路径
            var defaultFFmpegPath = Path.Combine(AppContext.BaseDirectory, "FFmpeg", "ffmpeg.exe");
            FFmpegPath = defaultFFmpegPath;

            // 设置默认导出模板路径
            var defaultTemplatePath = Path.Combine(AppContext.BaseDirectory, "Templates", "export_template.xlsx");
            ExportTemplatePath = defaultTemplatePath;
        }
    }
}