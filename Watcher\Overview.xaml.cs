using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Controls.Primitives;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI.Xaml.Navigation;
using System;
using System.Collections.Generic;
using Windows.Storage;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.ApplicationModel.DataTransfer;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.System;
using Microsoft.Extensions.DependencyInjection;
using CommunityToolkit.Mvvm.DependencyInjection;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Threading.Tasks;
// To learn more about WinUI, the WinUI project structure,
// and more about our project templates, see: http://aka.ms/winui-project-info.

namespace Watcher
{
    /// <summary>
    /// An empty page that can be used on its own or navigated to within a Frame.
    /// </summary>
    public sealed partial class Overview : Page
    {
        public OverviewViewModel ViewModel { get; set; }
        private bool _isScrollPending = false; // 防止重复滚动操作

        public Overview()
        {
            ViewModel = Ioc.Default.GetRequiredService<OverviewViewModel>();
            this.DataContext = ViewModel;
            this.InitializeComponent();
            
            // 设置 DispatcherQueue 用于UI线程调度
            ViewModel.SetDispatcherQueue(this.DispatcherQueue);

            // 订阅ChargeItems集合变化事件，当添加新项目时自动滚动到底部
            ViewModel.ChargeItems.CollectionChanged += ChargeItems_CollectionChanged;

            // 绑定删除按钮点击事件
            DeleteButton.Click += DeleteButton_Click;

            // 订阅ListView选择变化事件，用于控制删除按钮状态
            ChargeItemsListBox.SelectionChanged += ChargeItemsListBox_SelectionChanged;

            // 初始化删除按钮状态
            UpdateDeleteButtonState();
        }
        
        /// <summary>
        /// 当ChargeItems集合发生变化时，自动滚动到列表底部
        /// </summary>
        private void ChargeItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            // 只有当添加新项目时，才滚动到最底部
            if (e.Action == NotifyCollectionChangedAction.Add && e.NewItems != null && e.NewItems.Count > 0)
            {
                // 防止重复滚动操作
                if (_isScrollPending)
                    return;

                _isScrollPending = true;

                // 使用Dispatcher确保UI操作在UI线程上执行，并添加延迟以等待UI更新完成
                DispatcherQueue.TryEnqueue(() =>
                {
                    // 添加小延迟，确保ListView完全更新后再滚动
                    Task.Delay(50).ContinueWith(_ =>
                    {
                        DispatcherQueue.TryEnqueue(() =>
                        {
                            try
                            {
                                // 滚动到新添加的最后一个项目
                                var lastAddedItem = e.NewItems[e.NewItems.Count - 1];
                                ChargeItemsListBox.ScrollIntoView(lastAddedItem);
                                
                                // 自动选中最新添加的项目
                                ChargeItemsListBox.SelectedItem = lastAddedItem;
                            }
                            catch (Exception ex)
                            {
                                // 忽略滚动错误，避免影响用户体验
                                Debug.WriteLine($"滚动操作失败: {ex.Message}");
                            }
                            finally
                            {
                                _isScrollPending = false;
                            }
                        });
                    });
                });
            }
        }

        /// <summary>
        /// ListView选择变化事件处理
        /// </summary>
        private void ChargeItemsListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateDeleteButtonState();
        }

        /// <summary>
        /// 更新删除按钮的启用状态
        /// </summary>
        private void UpdateDeleteButtonState()
        {
            DeleteButton.IsEnabled = ChargeItemsListBox.SelectedItems != null && ChargeItemsListBox.SelectedItems.Count > 0;
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            // 获取选中的项目
            var selectedItems = ChargeItemsListBox.SelectedItems;

            if (selectedItems == null || selectedItems.Count == 0)
            {
                // 显示提示：没有选中项目
                var noSelectionDialog = new ContentDialog
                {
                    Title = "提示",
                    Content = "请先选择要删除的收费记录",
                    CloseButtonText = "确定",
                    XamlRoot = this.XamlRoot
                };
                await noSelectionDialog.ShowAsync();
                return;
            }

            // 显示确认删除对话框
            var confirmDialog = new ContentDialog
            {
                Title = "确认删除",
                Content = $"确定要删除选中的 {selectedItems.Count} 条收费记录吗？\n\n此操作不可撤销。",
                PrimaryButtonText = "删除",
                CloseButtonText = "取消",
                DefaultButton = ContentDialogButton.Close,
                XamlRoot = this.XamlRoot
            };

            var result = await confirmDialog.ShowAsync();
            if (result == ContentDialogResult.Primary)
            {
                try
                {
                    // 调用ViewModel的删除方法
                    await ViewModel.DeleteSelectedItemsAsync(selectedItems);

                    // 显示删除成功提示
                    var successDialog = new ContentDialog
                    {
                        Title = "删除成功",
                        Content = $"成功删除选中记录",
                        CloseButtonText = "确定",
                        XamlRoot = this.XamlRoot
                    };
                    await successDialog.ShowAsync();
                }
                catch (Exception ex)
                {
                    // 显示删除失败提示
                    var errorDialog = new ContentDialog
                    {
                        Title = "删除失败",
                        Content = $"删除收费记录时出现错误：{ex.Message}",
                        CloseButtonText = "确定",
                        XamlRoot = this.XamlRoot
                    };
                    await errorDialog.ShowAsync();
                }
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 创建日期选择对话框
                var dateSelectionDialog = CreateDateSelectionDialog();
                var result = await dateSelectionDialog.ShowAsync();

                if (result != ContentDialogResult.Primary)
                {
                    return; // 用户取消了操作
                }

                // 获取用户选择的日期和导出选项
                var selectedDate = GetSelectedDateFromDialog(dateSelectionDialog);
                var usePrintLayout = GetUsePrintLayoutFromDialog(dateSelectionDialog);

                // 检查选择的日期是否有收费记录
                var recordsForDate = ViewModel.ChargeItems.Where(item => item.Time.Date == selectedDate.Date).ToList();
                if (recordsForDate.Count == 0)
                {
                    // 显示无记录提示
                    var noDataDialog = new ContentDialog
                    {
                        Title = "提示",
                        Content = $"选择的日期 {selectedDate:yyyy-MM-dd} 没有收费记录可导出",
                        CloseButtonText = "确定",
                        XamlRoot = this.XamlRoot
                    };
                    await noDataDialog.ShowAsync();
                    return;
                }

                // 准备导出
                var defaultExportFolder = ViewModel._configService.GetLastExportFolder();
                var fileName = usePrintLayout ? $"收费记录_打印版_{selectedDate:yyyyMMdd}.xlsx" : $"收费记录_{selectedDate:yyyyMMdd}.xlsx";
                var savePath = Path.Combine(defaultExportFolder, fileName);

                // 确保导出目录存在
                var exportDir = Path.GetDirectoryName(savePath);
                if (!string.IsNullOrEmpty(exportDir))
                {
                    Directory.CreateDirectory(exportDir);

                    // 更新最后导出的文件夹路径
                    ViewModel._configService.UpdateLastExportFolder(exportDir);
                }

                // 根据用户选择执行相应的导出
                bool success;
                if (usePrintLayout)
                {
                    // 检查模板文件是否存在
                    var templatePath = ViewModel._configService.GetExportTemplatePath();
                    if (string.IsNullOrEmpty(templatePath) || !File.Exists(templatePath))
                    {
                        var templateErrorDialog = new ContentDialog
                        {
                            Title = "模板文件错误",
                            Content = "导出模板文件不存在或路径未配置，请检查配置设置。",
                            CloseButtonText = "确定",
                            XamlRoot = this.XamlRoot
                        };
                        await templateErrorDialog.ShowAsync();
                        return;
                    }

                    // 使用模板导出
                    success = await ViewModel.ExportDailyRecordsWithTemplateAsync(selectedDate, savePath, templatePath);
                }
                else
                {
                    // 使用普通导出
                    success = await ViewModel.ExportDailyRecordsAsync(selectedDate, savePath);
                }

                if (success)
                {
                    // 创建自定义对话框内容
                    var dialogContent = new StackPanel();
                    dialogContent.HorizontalAlignment = HorizontalAlignment.Center;
                    dialogContent.Children.Add(new TextBlock
                    {
                        Text = $"收费记录已成功导出到:\n{savePath}",
                        TextWrapping = Microsoft.UI.Xaml.TextWrapping.Wrap
                    });

                    // 创建按钮容器
                    var buttonPanel = new StackPanel
                    {
                        Orientation = Orientation.Horizontal,
                        Spacing = 10,
                        Margin = new Microsoft.UI.Xaml.Thickness(0, 20, 0, 0)
                    };

                    // 打开文件夹按钮
                    var openFolderButton = new Button
                    {
                        Content = "打开文件所在文件夹",
                        HorizontalAlignment = Microsoft.UI.Xaml.HorizontalAlignment.Left
                    };
                    openFolderButton.Click += async (s, e) =>
                    {
                        try
                        {
                            var folderPath = Path.GetDirectoryName(savePath);
                            if (!string.IsNullOrEmpty(folderPath))
                            {
                                await Windows.System.Launcher.LaunchFolderPathAsync(folderPath);
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"打开文件夹失败: {ex.Message}");
                        }
                    };

                    // 复制文件按钮
                    var copyFileButton = new Button
                    {
                        Content = "复制文件",
                        HorizontalAlignment = Microsoft.UI.Xaml.HorizontalAlignment.Left
                    };
                    copyFileButton.Click += async (s, e) =>
                    {
                        try
                        {
                            // 创建文件数据包
                            var dataPackage = new Windows.ApplicationModel.DataTransfer.DataPackage();
                            var storageFile = await Windows.Storage.StorageFile.GetFileFromPathAsync(savePath);
                            dataPackage.SetStorageItems(new List<Windows.Storage.IStorageItem> { storageFile });
                            Windows.ApplicationModel.DataTransfer.Clipboard.SetContent(dataPackage);
                            
                            // 可选：显示复制成功提示（使用轻量级通知而非对话框）
                            System.Diagnostics.Debug.WriteLine("文件已复制到剪贴板");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"复制文件失败: {ex.Message}");
                        }
                    };

                    buttonPanel.Children.Add(openFolderButton);
                    buttonPanel.Children.Add(copyFileButton);
                    dialogContent.Children.Add(buttonPanel);

                    // 显示成功消息
                    var successDialog = new ContentDialog
                    {
                        Title = "导出成功",
                        Content = dialogContent,
                        CloseButtonText = "确定",
                        XamlRoot = this.XamlRoot
                    };
                    
                    // 注意：这里不等待对话框关闭，让按钮事件处理程序异步执行
                    _ = successDialog.ShowAsync();
                }
                else
                {
                    // 显示失败消息
                    var errorDialog = new ContentDialog
                    {
                        Title = "导出失败",
                        Content = "导出Excel文件时出现错误，请重试",
                        CloseButtonText = "确定",
                        XamlRoot = this.XamlRoot
                    };
                    await errorDialog.ShowAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出操作失败: {ex.Message}");

                // 显示错误消息
                var errorDialog = new ContentDialog
                {
                    Title = "操作失败",
                    Content = $"导出过程中出现错误: {ex.Message}，文件可能被占用，关闭Excel/WPS后重试",
                    CloseButtonText = "确定",
                    XamlRoot = this.XamlRoot
                };
                await errorDialog.ShowAsync();
            }
        }

        /// <summary>
        /// 创建日期选择对话框
        /// </summary>
        /// <returns>日期选择对话框</returns>
        private ContentDialog CreateDateSelectionDialog()
        {
            // 创建对话框内容
            var dialogContent = new StackPanel();

            // 添加说明文本
            var instructionText = new TextBlock
            {
                Text = "请选择要导出的日期：",
                Margin = new Microsoft.UI.Xaml.Thickness(0, 0, 0, 10),
                FontSize = 14
            };
            dialogContent.Children.Add(instructionText);

            // 创建日期选择器
            var datePicker = new DatePicker
            {
                Header = "导出日期",
                // 设置默认日期为当前统计面板工作的日期
                Date = ViewModel.SelectedDate?.Date ?? DateTime.Now.Date,
                Margin = new Microsoft.UI.Xaml.Thickness(0, 0, 0, 15)
            };
            dialogContent.Children.Add(datePicker);

            // 创建使用打印排版复选框
            var usePrintLayoutCheckBox = new CheckBox
            {
                Content = "使用打印排版",
                IsChecked = false,
                Margin = new Microsoft.UI.Xaml.Thickness(0, 0, 0, 10)
            };
            dialogContent.Children.Add(usePrintLayoutCheckBox);

            // 添加说明文本
            var hintText = new TextBlock
            {
                Text = "勾选后将使用Excel模板进行格式化导出，适合打印",
                Margin = new Microsoft.UI.Xaml.Thickness(20, 0, 0, 0),
                FontSize = 12,
                Foreground = new Microsoft.UI.Xaml.Media.SolidColorBrush(Microsoft.UI.Colors.Gray)
            };
            dialogContent.Children.Add(hintText);

            // 创建对话框
            var dialog = new ContentDialog
            {
                Title = "选择导出选项",
                Content = dialogContent,
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                XamlRoot = this.XamlRoot
            };

            return dialog;
        }

        /// <summary>
        /// 从对话框中获取选择的日期
        /// </summary>
        /// <param name="dialog">日期选择对话框</param>
        /// <returns>选择的日期</returns>
        private DateTime GetSelectedDateFromDialog(ContentDialog dialog)
        {
            if (dialog.Content is StackPanel stackPanel)
            {
                // 查找DatePicker控件
                var datePicker = stackPanel.Children.OfType<DatePicker>().FirstOrDefault();
                if (datePicker != null)
                {
                    return datePicker.Date.DateTime;
                }
            }

            // 如果找不到DatePicker，返回当前日期
            return DateTime.Now.Date;
        }

        /// <summary>
        /// 从对话框中获取是否使用打印排版选项
        /// </summary>
        /// <param name="dialog">日期选择对话框</param>
        /// <returns>是否使用打印排版</returns>
        private bool GetUsePrintLayoutFromDialog(ContentDialog dialog)
        {
            if (dialog.Content is StackPanel stackPanel)
            {
                // 查找CheckBox控件
                var checkBox = stackPanel.Children.OfType<CheckBox>().FirstOrDefault();
                if (checkBox != null)
                {
                    return checkBox.IsChecked == true;
                }
            }

            return false;
        }

    }
}
